"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Ad from "@/components/Ad";
import ThemeToggle from "@/components/ThemeToggle";
import EnhancedBackground from "@/components/EnhancedBackground";
import EnhancedButton from "@/components/EnhancedButton";
import ProgressiveLoader from "@/components/ProgressiveLoader";
import ShareButton from "@/components/ShareButton";
import PersonalizedTips from "@/components/PersonalizedTips";

const WaterDrop = ({ size = "lg", animate = true }) => {
  const dimensions = size === "sm" ? { w: 24, h: 24 } : { w: 48, h: 48 };

  return (
    <motion.svg
      initial={animate ? { y: -10 } : false}
      animate={animate ? { y: 0 } : false}
      transition={{ duration: 0.8, repeat: Infinity, repeatType: "reverse" }}
      width={dimensions.w} height={dimensions.h} viewBox="0 0 48 48" fill="none"
      className="mx-auto drop-shadow-lg"
    >
      <defs>
        <linearGradient id="waterGradient" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" stopColor="#0ea5e9" />
          <stop offset="50%" stopColor="#38bdf8" />
          <stop offset="100%" stopColor="#0284c7" />
        </linearGradient>
        <linearGradient id="shadowGradient" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" stopColor="#0369a1" stopOpacity="0.3" />
          <stop offset="100%" stopColor="#0c4a6e" stopOpacity="0.6" />
        </linearGradient>
      </defs>
      <ellipse cx="24" cy="38" rx="10" ry="6" fill="url(#shadowGradient)" />
      <path d="M24 6C24 6 10 22 10 32C10 39 17 44 24 44C31 44 38 39 38 32C38 22 24 6 24 6Z" fill="url(#waterGradient)" />
      <ellipse cx="24" cy="32" rx="6" ry="3" fill="#bae6fd" opacity="0.8" />
      <circle cx="20" cy="26" r="1.5" fill="#ffffff" opacity="0.9" />
      <circle cx="28" cy="30" r="1" fill="#ffffff" opacity="0.6" />
    </motion.svg>
  );
};

const FloatingElements = () => (
  <>
    {[...Array(8)].map((_, i) => (
      <motion.div
        key={i}
        className="absolute w-1.5 h-1.5 bg-primary-300/40 dark:bg-primary-400/30 rounded-full"
        animate={{
          y: [0, -30, 0],
          x: [0, Math.sin(i) * 15, 0],
          scale: [1, 1.3, 1],
          opacity: [0.3, 0.7, 0.3],
        }}
        transition={{
          duration: 4 + i * 0.7,
          repeat: Infinity,
          delay: i * 0.4,
          ease: "easeInOut",
        }}
        style={{
          left: `${15 + i * 12}%`,
          top: `${25 + (i % 4) * 20}%`,
        }}
      />
    ))}
    {[...Array(4)].map((_, i) => (
      <motion.div
        key={`bubble-${i}`}
        className="absolute w-3 h-3 bg-secondary-200/30 dark:bg-secondary-300/20 rounded-full"
        animate={{
          y: [0, -40, 0],
          scale: [1, 1.5, 1],
          opacity: [0.2, 0.5, 0.2],
        }}
        transition={{
          duration: 6 + i * 1.2,
          repeat: Infinity,
          delay: i * 1.5,
          ease: "easeInOut",
        }}
        style={{
          right: `${10 + i * 20}%`,
          top: `${40 + (i % 2) * 30}%`,
        }}
      />
    ))}
  </>
);

type ProgressBarProps = {
  current: number;
  total: number;
  label: string;
};
const ProgressBar = ({ current, total, label }: ProgressBarProps) => (
  <div className="w-full">
    <div className="flex justify-between text-xs text-primary-600 dark:text-primary-400 mb-2">
      <span className="font-medium">{label}</span>
      <span className="font-semibold">{current.toFixed(1)}L / {total.toFixed(1)}L</span>
    </div>
    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 overflow-hidden shadow-inner">
      <motion.div
        className="bg-gradient-to-r from-primary-500 to-secondary-500 h-3 rounded-full relative overflow-hidden"
        initial={{ width: 0 }}
        animate={{ width: `${Math.min((current / total) * 100, 100)}%` }}
        transition={{ duration: 1.2, ease: "easeOut" }}
      >
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
          animate={{ x: ['-100%', '100%'] }}
          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
        />
      </motion.div>
    </div>
  </div>
);

type InputFieldProps = {
  id: string;
  label: string;
  type: string;
  value: string | number;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
  unit?: string;
  min?: string | number;
  max?: string | number;
  step?: string | number;
  tooltip?: string;
  icon?: React.ReactNode;
  error?: string;
};
const InputField = ({ id, label, type, value, onChange, placeholder, unit, min, max, step, tooltip, icon, error }: InputFieldProps) => (
  <motion.div
    initial={{ x: -20, opacity: 0 }}
    animate={{ x: 0, opacity: 1 }}
    className="group relative"
    whileHover={{ scale: 1.01 }}
    transition={{ duration: 0.2 }}
  >
    <motion.label
      htmlFor={id}
      className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2"
      whileHover={{ x: 2 }}
      transition={{ duration: 0.2 }}
    >
      <motion.span
        className="text-primary-500 dark:text-primary-400 text-lg"
        whileHover={{ scale: 1.2, rotate: 5 }}
        transition={{ duration: 0.2 }}
      >
        {icon}
      </motion.span>
      {label}
      <div className="relative group/tooltip">
        <motion.span
          className="text-primary-400 dark:text-primary-300 cursor-help text-xs hover:text-primary-600 dark:hover:text-primary-200 transition-colors"
          title={tooltip}
          whileHover={{ scale: 1.2 }}
          whileTap={{ scale: 0.9 }}
        >
          ⓘ
        </motion.span>
        <motion.div
          className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-xs rounded-lg opacity-0 group-hover/tooltip:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10"
          initial={{ scale: 0.8, y: 5 }}
          whileHover={{ scale: 1, y: 0 }}
        >
          {tooltip}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900 dark:border-t-gray-100"></div>
        </motion.div>
      </div>
    </motion.label>
    <div className="relative">
      <motion.input
        type={type}
        id={id}
        min={min}
        max={max}
        step={step}
        value={value}
        onChange={onChange}
        className={`block w-full px-4 py-4 rounded-xl border-2 transition-all duration-300 text-lg font-medium backdrop-blur-sm
          ${error
            ? 'border-red-300 dark:border-red-600 focus:border-red-500 focus:ring-red-200 dark:focus:ring-red-800/50 bg-red-50 dark:bg-red-900/20 text-red-900 dark:text-red-100'
            : 'border-gray-200 dark:border-gray-700 focus:border-primary-500 focus:ring-primary-200 dark:focus:ring-primary-800/50 bg-white/80 dark:bg-gray-800/80 hover:bg-white dark:hover:bg-gray-800 focus:bg-white dark:focus:bg-gray-800 text-gray-900 dark:text-gray-100'
          } focus:ring-4 focus:outline-none shadow-sm hover:shadow-md focus:shadow-lg placeholder-gray-400 dark:placeholder-gray-500`}
        placeholder={placeholder}
        whileFocus={{ scale: 1.02 }}
        transition={{ duration: 0.2 }}
      />
      {unit && (
        <motion.span
          className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 font-medium pointer-events-none"
          initial={{ opacity: 0, x: 10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
        >
          {unit}
        </motion.span>
      )}
      {error && (
        <motion.span
          initial={{ opacity: 0, y: -10, scale: 0.8 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          className="absolute -bottom-6 left-0 text-red-500 dark:text-red-400 text-xs font-medium flex items-center gap-1"
        >
          <motion.span
            animate={{ rotate: [0, -10, 10, 0] }}
            transition={{ duration: 0.5, repeat: 2 }}
          >
            ⚠️
          </motion.span>
          {error}
        </motion.span>
      )}
    </div>
  </motion.div>
);

type ClimateSelectorProps = {
  value: string;
  onChange: (v: string) => void;
  error?: string;
};
const ClimateSelector = ({ value, onChange }: ClimateSelectorProps) => (
  <motion.div
    initial={{ x: -20, opacity: 0 }}
    animate={{ x: 0, opacity: 1 }}
    className="group relative"
  >
    <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2">
      <span className="text-orange-500 text-lg">🌡️</span>
      Climate Conditions
    </label>
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
      {[
        { value: "Normal", label: "Normal", icon: "🌤️", desc: "Mild weather conditions", color: "emerald" },
        { value: "Hot", label: "Hot", icon: "☀️", desc: "High temperature & humidity", color: "orange" }
      ].map((option) => (
        <motion.button
          key={option.value}
          type="button"
          whileHover={{
            scale: 1.03,
            y: -3,
            boxShadow: value === option.value
              ? "0 20px 40px -12px rgba(59, 130, 246, 0.3)"
              : "0 10px 20px -5px rgba(0, 0, 0, 0.1)"
          }}
          whileTap={{ scale: 0.97 }}
          onClick={() => onChange(option.value)}
          className={`relative p-4 rounded-xl border-2 transition-all duration-300 text-left backdrop-blur-sm group/option overflow-hidden
            ${value === option.value
              ? `border-primary-500 dark:border-primary-400 bg-primary-50 dark:bg-primary-900/30 shadow-lg shadow-primary-500/20`
              : 'border-gray-200 dark:border-gray-700 bg-white/80 dark:bg-gray-800/80 hover:border-primary-300 dark:hover:border-primary-600 hover:bg-primary-25 dark:hover:bg-primary-900/20 hover:shadow-md'
            }`}
        >
          {/* Ripple effect on selection */}
          {value === option.value && (
            <motion.div
              className="absolute inset-0 bg-primary-400/10 rounded-xl"
              initial={{ scale: 0, opacity: 1 }}
              animate={{ scale: 1, opacity: 0 }}
              transition={{ duration: 0.6 }}
            />
          )}

          {/* Hover glow effect */}
          <motion.div
            className={`absolute inset-0 rounded-xl opacity-0 group-hover/option:opacity-100 transition-opacity duration-300 ${
              option.color === 'emerald' ? 'bg-emerald-500/5' : 'bg-orange-500/5'
            }`}
            whileHover={{ scale: 1.05 }}
          />
          <div className="flex items-center gap-3 mb-2 relative z-10">
            <motion.span
              className="text-2xl"
              whileHover={{ scale: 1.2, rotate: 5 }}
              transition={{ duration: 0.2 }}
            >
              {option.icon}
            </motion.span>
            <motion.span
              className={`font-semibold ${value === option.value ? 'text-primary-700 dark:text-primary-300' : 'text-gray-800 dark:text-gray-200'}`}
              whileHover={{ x: 2 }}
              transition={{ duration: 0.2 }}
            >
              {option.label}
            </motion.span>
          </div>
          <motion.span
            className={`text-xs relative z-10 ${value === option.value ? 'text-primary-600 dark:text-primary-400' : 'text-gray-500 dark:text-gray-400'}`}
            initial={{ opacity: 0.8 }}
            whileHover={{ opacity: 1 }}
          >
            {option.desc}
          </motion.span>
          {value === option.value && (
            <motion.div
              layoutId="climate-indicator"
              className="absolute top-3 right-3 w-3 h-3 bg-primary-500 dark:bg-primary-400 rounded-full shadow-lg z-20"
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ type: "spring", stiffness: 500, damping: 30 }}
            >
              <motion.div
                className="absolute inset-0 bg-primary-400 dark:bg-primary-300 rounded-full"
                animate={{ scale: [1, 1.5, 1], opacity: [1, 0, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              />
            </motion.div>
          )}
        </motion.button>
      ))}
    </div>
  </motion.div>
);

type Breakdown = {
  base: number;
  exercise: number;
  climate: number;
  total: number;
};
type Errors = {
  weight?: string;
  exercise?: string;
  climate?: string;
};
export default function Home() {
  const [weight, setWeight] = useState<string>("");
  const [exercise, setExercise] = useState<string>("");
  const [climate, setClimate] = useState<string>("Normal");
  const [result, setResult] = useState<number | null>(null);
  const [isCalculating, setIsCalculating] = useState<boolean>(false);
  const [errors, setErrors] = useState<Errors>({});
  const [showBreakdown, setShowBreakdown] = useState<boolean>(false);
  const [breakdown, setBreakdown] = useState<Breakdown | null>(null);
  const [quote, setQuote] = useState<string>("");
  const [announceResult, setAnnounceResult] = useState<string>("");

  useEffect(() => {
    const quotes = [
      "Water is life. Drink up!",
      "Stay hydrated, stay healthy.",
      "A hydrated body is a happy body.",
      "Sip, smile, repeat.",
      "Hydration fuels your day!"
    ];
    setQuote(quotes[Math.floor(Math.random() * quotes.length)]);
  }, []);

  const validateInputs = () => {
    const newErrors: Errors = {};
    if (!weight || Number(weight) <= 0) {
      newErrors.weight = "Weight must be greater than 0";
    }
    if (!exercise || Number(exercise) < 0) {
      newErrors.exercise = "Exercise hours cannot be negative";
    }
    if (Number(exercise) > 24) {
      newErrors.exercise = "Exercise hours cannot exceed 24";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const calculateWaterIntake = async () => {
    if (!validateInputs()) return;

    setIsCalculating(true);
    setAnnounceResult("Calculating your personalized water intake...");
    await new Promise(resolve => setTimeout(resolve, 800));

    const weightNum = Number(weight);
    const exerciseNum = Number(exercise);

    // Base water intake (35ml per kg)
    const baseIntake = weightNum * 0.035;

    // Exercise adjustment (500ml per hour)
    const exerciseIntake = exerciseNum * 0.5;

    // Climate adjustment
    const climateIntake = climate === "Hot" ? 0.7 : 0;

    const totalIntake = baseIntake + exerciseIntake + climateIntake;
    const finalResult = Number(totalIntake.toFixed(2));

    setResult(finalResult);
    setBreakdown({
      base: baseIntake,
      exercise: exerciseIntake,
      climate: climateIntake,
      total: totalIntake
    });
    setIsCalculating(false);
    setShowBreakdown(true);
    setAnnounceResult(`Your daily water goal is ${finalResult} liters. That's about ${Math.round(finalResult * 4)} cups of water.`);
  };

  const reset = () => {
    setWeight("");
    setExercise("");
    setClimate("Normal");
    setResult(null);
    setErrors({});
    setShowBreakdown(false);
    setBreakdown(null);
    setAnnounceResult("Calculator has been reset. Please enter your information again.");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-secondary-50 to-primary-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 py-4 px-2 sm:px-4 lg:px-8 relative overflow-hidden">
      {/* Progressive Loader */}
      <ProgressiveLoader
        isLoading={isCalculating}
        steps={[
          "Analyzing your body weight...",
          "Calculating exercise requirements...",
          "Adjusting for climate conditions...",
          "Finalizing your hydration plan..."
        ]}
        duration={800}
      />

      {/* Screen Reader Announcements */}
      <div
        className="sr-only"
        aria-live="polite"
        aria-atomic="true"
        role="status"
      >
        {announceResult}
      </div>

      {/* Skip to main content link */}
      <a
        href="#main-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 px-4 py-2 bg-primary-600 text-white rounded-lg font-medium focus:outline-none focus:ring-2 focus:ring-primary-300"
      >
        Skip to main content
      </a>

      {/* Theme Toggle */}
      <div className="absolute top-4 right-4 z-50">
        <ThemeToggle />
      </div>

      {/* Enhanced Background */}
      <EnhancedBackground />

      <FloatingElements />

      <div className="relative z-10 flex items-center justify-center min-h-screen py-8">
        <div className="w-full max-w-2xl mx-auto">{/* Main content will go here */}

          <motion.main
            id="main-content"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="relative bg-white/95 dark:bg-gray-900/95 backdrop-blur-2xl rounded-3xl shadow-2xl p-4 sm:p-6 md:p-8 border border-white/30 dark:border-gray-700/30 focus-within:ring-2 focus-within:ring-primary-500/20 overflow-hidden"
            style={{
              boxShadow: "0 32px 64px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1)"
            }}
            role="main"
            aria-labelledby="calculator-title"
          >
            {/* Card shine effect */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent pointer-events-none"
              initial={{ opacity: 0 }}
              animate={{ opacity: [0, 0.5, 0] }}
              transition={{ duration: 3, repeat: Infinity, repeatDelay: 5 }}
            />

            {/* Subtle border glow */}
            <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-primary-500/20 via-secondary-500/20 to-primary-500/20 blur-sm -z-10" />
            {/* Header */}
            <div className="text-center mb-8 sm:mb-10">
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.5 }}
              >
                <WaterDrop />
              </motion.div>
              <motion.h1
                id="calculator-title"
                className="text-3xl xs:text-4xl sm:text-5xl font-bold mt-6 mb-3 bg-gradient-to-r from-primary-600 via-secondary-600 to-primary-700 dark:from-primary-400 dark:via-secondary-400 dark:to-primary-500 bg-clip-text text-transparent"
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                Hydration Calculator
              </motion.h1>
              <motion.p
                className="text-gray-600 dark:text-gray-300 text-base sm:text-lg mb-3 font-medium"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                Calculate your personalized daily water intake
              </motion.p>
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
                className="inline-flex items-center gap-2 px-4 py-2 bg-primary-50 dark:bg-primary-900/30 rounded-full border border-primary-200 dark:border-primary-700"
              >
                <span className="text-primary-600 dark:text-primary-400 text-sm font-medium">💧</span>
                <span className="text-primary-700 dark:text-primary-300 text-sm italic font-medium" aria-live="polite">
                  {quote}
                </span>
              </motion.div>
            </div>

            <div className="space-y-6 sm:space-y-8">
              {/* Hidden description for screen readers */}
              <div id="calculation-description" className="sr-only">
                This calculator determines your daily water intake based on your body weight, exercise duration, and climate conditions. Results are provided in liters and cups.
              </div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8 }}
              >
                <InputField
                  id="weight"
                  label="Body Weight"
                  type="number"
                  value={weight}
                  onChange={(e) => setWeight(e.target.value)}
                  placeholder="Enter your weight"
                  unit="kg"
                  min="1"
                  max="500"
                  step="0.1"
                  tooltip="Your current body weight in kilograms"
                  icon="⚖️"
                  error={errors.weight}
                />
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.0 }}
              >
                <InputField
                  id="exercise"
                  label="Daily Exercise"
                  type="number"
                  value={exercise}
                  onChange={(e) => setExercise(e.target.value)}
                  placeholder="Hours of exercise"
                  unit="hours"
                  min="0"
                  max="24"
                  step="0.25"
                  tooltip="Total hours of physical activity per day"
                  icon="🏃‍♂️"
                  error={errors.exercise}
                />
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.2 }}
              >
                <ClimateSelector
                  value={climate}
                  onChange={setClimate}
                />
              </motion.div>

              <motion.div
                className="flex flex-col sm:flex-row gap-4 pt-4 sm:pt-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.4 }}
              >
                <EnhancedButton
                  onClick={calculateWaterIntake}
                  disabled={isCalculating}
                  loading={isCalculating}
                  variant="primary"
                  size="lg"
                  className="w-full sm:flex-1"
                  icon={!isCalculating ? <WaterDrop size="sm" animate={false} /> : undefined}
                  aria-label={isCalculating ? "Calculating your water intake, please wait" : "Calculate your recommended daily water intake"}
                >
                  {isCalculating ? "Calculating..." : "Calculate Intake"}
                </EnhancedButton>

                {result && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                  >
                    <EnhancedButton
                      onClick={reset}
                      variant="secondary"
                      size="lg"
                      className="w-full sm:w-auto"
                      icon="🔄"
                      aria-label="Reset calculator"
                    >
                      Reset
                    </EnhancedButton>
                  </motion.div>
                )}
              </motion.div>
            </div>

            <AnimatePresence>
              {result !== null && (
                <motion.div
                  initial={{ opacity: 0, y: 30, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: -30, scale: 0.95 }}
                  transition={{ duration: 0.5, type: "spring", stiffness: 300, damping: 30 }}
                  className="relative mt-8 sm:mt-10 p-6 sm:p-8 bg-gradient-to-br from-primary-50/90 via-secondary-50/90 to-primary-100/90 dark:from-gray-800/90 dark:via-gray-700/90 dark:to-gray-800/90 rounded-2xl border border-primary-200/50 dark:border-gray-600/50 shadow-2xl backdrop-blur-xl overflow-hidden"
                  style={{
                    boxShadow: "0 20px 40px -12px rgba(59, 130, 246, 0.15), 0 8px 16px -4px rgba(16, 185, 129, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1)"
                  }}
                >
                  {/* Result card glow effect */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-primary-400/10 via-secondary-400/10 to-primary-400/10 rounded-2xl"
                    animate={{
                      background: [
                        "linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(16, 185, 129, 0.1), rgba(59, 130, 246, 0.1))",
                        "linear-gradient(45deg, rgba(16, 185, 129, 0.1), rgba(59, 130, 246, 0.1), rgba(16, 185, 129, 0.1))",
                        "linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(16, 185, 129, 0.1), rgba(59, 130, 246, 0.1))"
                      ]
                    }}
                    transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
                  />

                  {/* Celebration particles */}
                  <div className="absolute inset-0 pointer-events-none">
                    {[...Array(8)].map((_, i) => (
                      <motion.div
                        key={i}
                        className="absolute w-1 h-1 bg-primary-400/60 rounded-full"
                        style={{
                          left: `${20 + i * 10}%`,
                          top: `${30 + (i % 3) * 20}%`,
                        }}
                        animate={{
                          y: [0, -20, 0],
                          opacity: [0, 1, 0],
                          scale: [0, 1, 0],
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          delay: i * 0.2,
                          ease: "easeInOut",
                        }}
                      />
                    ))}
                  </div>
                  <div className="text-center mb-6 sm:mb-8">
                    <motion.div
                      initial={{ scale: 0, rotate: -180 }}
                      animate={{ scale: 1, rotate: 0 }}
                      transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                    >
                      <WaterDrop size="sm" />
                    </motion.div>
                    <div className="flex items-center justify-between mt-4 mb-3">
                      <motion.h3
                        className="text-xl sm:text-2xl font-bold text-primary-800 dark:text-primary-200"
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3 }}
                      >
                        Your Daily Water Goal
                      </motion.h3>
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.4 }}
                      >
                        <ShareButton
                          result={result}
                          weight={weight}
                          exercise={exercise}
                          climate={climate}
                        />
                      </motion.div>
                    </div>
                    <div className="flex items-center justify-center space-x-3 mb-4">
                      <motion.span
                        className="text-4xl sm:text-5xl font-bold bg-gradient-to-r from-primary-600 to-secondary-600 dark:from-primary-400 dark:to-secondary-400 bg-clip-text text-transparent"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 0.4, type: "spring", stiffness: 300 }}
                      >
                        {result}
                      </motion.span>
                      <motion.span
                        className="text-xl sm:text-2xl text-primary-500 dark:text-primary-400 font-semibold"
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.5 }}
                      >
                        Liters
                      </motion.span>
                    </div>
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.6 }}
                      className="inline-flex items-center gap-2 px-4 py-2 bg-white/60 dark:bg-gray-700/60 rounded-full border border-primary-200/50 dark:border-gray-600/50"
                    >
                      <span className="text-2xl">🥤</span>
                      <span className="text-sm sm:text-base text-primary-700 dark:text-primary-300 font-medium">
                        That's about {Math.round(result * 4)} cups of water!
                      </span>
                    </motion.div>
                  </div>

                  {showBreakdown && breakdown && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      transition={{ delay: 0.7, duration: 0.5 }}
                      className="space-y-4 pt-6 border-t border-primary-200/50 dark:border-gray-600/50"
                    >
                      <motion.h4
                        className="font-bold text-primary-800 dark:text-primary-200 text-sm sm:text-base mb-4 flex items-center gap-2"
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.8 }}
                      >
                        <span className="text-lg">📊</span>
                        Detailed Breakdown
                      </motion.h4>
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.9 }}
                      >
                        <ProgressBar current={breakdown.base} total={breakdown.total} label="Base hydration needs" />
                      </motion.div>
                      {breakdown.exercise > 0 && (
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 1.0 }}
                        >
                          <ProgressBar current={breakdown.exercise} total={breakdown.total} label="Exercise boost" />
                        </motion.div>
                      )}
                      {breakdown.climate > 0 && (
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 1.1 }}
                        >
                          <ProgressBar current={breakdown.climate} total={breakdown.total} label="Climate adjustment" />
                        </motion.div>
                      )}
                    </motion.div>
                  )}

                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 1.2 }}
                    className="mt-6 sm:mt-8 p-4 sm:p-6 bg-white/70 dark:bg-gray-800/70 rounded-xl border border-primary-200/30 dark:border-gray-600/30 backdrop-blur-sm"
                  >
                    <PersonalizedTips
                      result={result}
                      weight={Number(weight)}
                      exercise={Number(exercise)}
                      climate={climate}
                    />
                  </motion.div>

                  <motion.div
                    className="mt-6 flex justify-center"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1.8 }}
                  >
                    <Ad slot="1234567890" />
                  </motion.div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.main>
        </div>
      </div>
    </div>
  );
}