"use client";


import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Ad from "@/components/Ad";

const WaterDrop = ({ size = "lg", animate = true }) => {
  const dimensions = size === "sm" ? { w: 24, h: 24 } : { w: 48, h: 48 };
  
  return (
    <motion.svg
      initial={animate ? { y: -10 } : false}
      animate={animate ? { y: 0 } : false}
      transition={{ duration: 0.8, repeat: Infinity, repeatType: "reverse" }}
      width={dimensions.w} height={dimensions.h} viewBox="0 0 48 48" fill="none"
      className="mx-auto"
    >
      <ellipse cx="24" cy="38" rx="10" ry="6" fill="#38bdf8" />
      <path d="M24 6C24 6 10 22 10 32C10 39 17 44 24 44C31 44 38 39 38 32C38 22 24 6 24 6Z" fill="#0ea5e9" />
      <ellipse cx="24" cy="32" rx="6" ry="3" fill="#bae6fd" />
      <circle cx="20" cy="26" r="1.5" fill="#ffffff" opacity="0.7" />
    </motion.svg>
  );
};

const FloatingElements = () => (
  <>
    {[...Array(6)].map((_, i) => (
      <motion.div
        key={i}
        className="absolute w-2 h-2 bg-blue-200 rounded-full opacity-30"
        animate={{
          y: [0, -20, 0],
          x: [0, Math.sin(i) * 10, 0],
          scale: [1, 1.2, 1],
        }}
        transition={{
          duration: 3 + i * 0.5,
          repeat: Infinity,
          delay: i * 0.3,
        }}
        style={{
          left: `${20 + i * 15}%`,
          top: `${30 + (i % 3) * 20}%`,
        }}
      />
    ))}
  </>
);

type ProgressBarProps = {
  current: number;
  total: number;
  label: string;
};
const ProgressBar = ({ current, total, label }: ProgressBarProps) => (
  <div className="w-full">
    <div className="flex justify-between text-xs text-blue-600 mb-1">
      <span>{label}</span>
      <span>{current.toFixed(1)}L / {total.toFixed(1)}L</span>
    </div>
    <div className="w-full bg-blue-100 rounded-full h-2">
      <motion.div
        className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full"
        initial={{ width: 0 }}
        animate={{ width: `${Math.min((current / total) * 100, 100)}%` }}
        transition={{ duration: 1, ease: "easeOut" }}
      />
    </div>
  </div>
);

type InputFieldProps = {
  id: string;
  label: string;
  type: string;
  value: string | number;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
  unit?: string;
  min?: string | number;
  max?: string | number;
  step?: string | number;
  tooltip?: string;
  icon?: React.ReactNode;
  error?: string;
};
const InputField = ({ id, label, type, value, onChange, placeholder, unit, min, max, step, tooltip, icon, error }: InputFieldProps) => (
  <motion.div
    initial={{ x: -20, opacity: 0 }}
    animate={{ x: 0, opacity: 1 }}
    className="group relative"
  >
    <label htmlFor={id} className="block text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
      <span className="text-blue-500">{icon}</span>
      {label}
      <div className="relative">
        <span className="text-blue-400 cursor-help text-xs" title={tooltip}>ⓘ</span>
      </div>
    </label>
    <div className="relative">
      <input
        type={type}
        id={id}
        min={min}
        max={max}
        step={step}
        value={value}
        onChange={onChange}
        className={`block w-full px-4 py-4 rounded-xl border-2 transition-all duration-300 text-lg font-medium
          ${error 
            ? 'border-red-300 focus:border-red-500 focus:ring-red-200 bg-red-50' 
            : 'border-gray-200 focus:border-blue-500 focus:ring-blue-200 bg-gray-50 hover:bg-white focus:bg-white'
          } focus:ring-4 focus:outline-none shadow-sm hover:shadow-md focus:shadow-lg`}
        placeholder={placeholder}
      />
      {unit && (
        <span className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 font-medium">
          {unit}
        </span>
      )}
      {error && (
        <motion.span
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="absolute -bottom-6 left-0 text-red-500 text-xs font-medium"
        >
          {error}
        </motion.span>
      )}
    </div>
  </motion.div>
);

type ClimateSelectorProps = {
  value: string;
  onChange: (v: string) => void;
  error?: string;
};
const ClimateSelector = ({ value, onChange, error }: ClimateSelectorProps) => (
  <motion.div
    initial={{ x: -20, opacity: 0 }}
    animate={{ x: 0, opacity: 1 }}
    className="group relative"
  >
    <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
      <span className="text-orange-500">🌡️</span>
      Climate Conditions
    </label>
    <div className="grid grid-cols-2 gap-3">
      {[
        { value: "Normal", label: "Normal", icon: "🌤️", desc: "Mild weather" },
        { value: "Hot", label: "Hot", icon: "☀️", desc: "High temperature" }
      ].map((option) => (
        <motion.button
          key={option.value}
          type="button"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => onChange(option.value)}
          className={`relative p-4 rounded-xl border-2 transition-all duration-300 text-left
            ${value === option.value 
              ? 'border-blue-500 bg-blue-50 shadow-md' 
              : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-25'
            }`}
        >
          <div className="flex items-center gap-2 mb-1">
            <span className="text-xl">{option.icon}</span>
            <span className="font-semibold text-gray-800">{option.label}</span>
          </div>
          <span className="text-xs text-gray-500">{option.desc}</span>
          {value === option.value && (
            <motion.div
              layoutId="climate-indicator"
              className="absolute top-2 right-2 w-3 h-3 bg-blue-500 rounded-full"
            />
          )}
        </motion.button>
      ))}
    </div>
  </motion.div>
);

type Breakdown = {
  base: number;
  exercise: number;
  climate: number;
  total: number;
};
type Errors = {
  weight?: string;
  exercise?: string;
  climate?: string;
};
export default function Home() {
  const [weight, setWeight] = useState<string>("");
  const [exercise, setExercise] = useState<string>("");
  const [climate, setClimate] = useState<string>("Normal");
  const [result, setResult] = useState<number | null>(null);
  const [isCalculating, setIsCalculating] = useState<boolean>(false);
  const [errors, setErrors] = useState<Errors>({});
  const [showBreakdown, setShowBreakdown] = useState<boolean>(false);
  const [breakdown, setBreakdown] = useState<Breakdown | null>(null);
  const [quote, setQuote] = useState<string>("");

  useEffect(() => {
    const quotes = [
      "Water is life. Drink up!",
      "Stay hydrated, stay healthy.",
      "A hydrated body is a happy body.",
      "Sip, smile, repeat.",
      "Hydration fuels your day!"
    ];
    setQuote(quotes[Math.floor(Math.random() * quotes.length)]);
  }, []);

  const validateInputs = () => {
    const newErrors: Errors = {};
    if (!weight || Number(weight) <= 0) {
      newErrors.weight = "Weight must be greater than 0";
    }
    if (!exercise || Number(exercise) < 0) {
      newErrors.exercise = "Exercise hours cannot be negative";
    }
    if (Number(exercise) > 24) {
      newErrors.exercise = "Exercise hours cannot exceed 24";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const calculateWaterIntake = async () => {
    if (!validateInputs()) return;
    
    setIsCalculating(true);
    await new Promise(resolve => setTimeout(resolve, 800));
    
    const weightNum = Number(weight);
    const exerciseNum = Number(exercise);
    
    // Base water intake (35ml per kg)
    const baseIntake = weightNum * 0.035;
    
    // Exercise adjustment (500ml per hour)
    const exerciseIntake = exerciseNum * 0.5;
    
    // Climate adjustment
    const climateIntake = climate === "Hot" ? 0.7 : 0;
    
    const totalIntake = baseIntake + exerciseIntake + climateIntake;
    
    setResult(Number(totalIntake.toFixed(2)));
    setBreakdown({
      base: baseIntake,
      exercise: exerciseIntake,
      climate: climateIntake,
      total: totalIntake
    });
    setIsCalculating(false);
    setShowBreakdown(true);
  };

  const reset = () => {
    setWeight("");
    setExercise("");
    setClimate("Normal");
    setResult(null);
    setErrors({});
    setShowBreakdown(false);
    setBreakdown(null);
  };

  return (
  <div className="min-h-screen bg-gradient-to-br from-blue-200 via-cyan-100 to-blue-50 py-4 px-1 sm:px-4 lg:px-8 relative overflow-hidden flex items-center justify-center">
      {/* Animated Gradient Background */}
      <motion.div
        initial={{ backgroundPosition: "0% 50%" }}
        animate={{ backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"] }}
        transition={{ duration: 12, repeat: Infinity, ease: "linear" }}
        className="absolute inset-0 w-full h-full z-0"
        style={{
          background: "linear-gradient(120deg, #bae6fd 0%, #a7f3d0 50%, #f0fdfa 100%)",
          backgroundSize: "200% 200%"
        }}
      />
      <FloatingElements />
      {/* Decorative wave */}
      <svg className="absolute left-0 top-0 w-full h-32 pointer-events-none opacity-60" viewBox="0 0 1440 320" fill="none">
        <path fill="url(#gradient)" d="M0,160L60,170.7C120,181,240,203,360,197.3C480,192,600,160,720,154.7C840,149,960,171,1080,186.7C1200,203,1320,213,1380,218.7L1440,224L1440,0L1380,0C1320,0,1200,0,1080,0C960,0,840,0,720,0C600,0,480,0,360,0C240,0,120,0,60,0L0,0Z" />
        <defs>
          <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#bae6fd" stopOpacity="0.4"/>
            <stop offset="100%" stopColor="#0ea5e9" stopOpacity="0.2"/>
          </linearGradient>
        </defs>
      </svg>

      <motion.div 
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="max-w-lg w-full mx-auto bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl p-3 sm:p-6 md:p-8 relative z-10 border border-white/50"
        style={{ boxShadow: "0 8px 32px 0 rgba(56,189,248,0.15), 0 1.5px 6px 0 rgba(14,165,233,0.08)" }}
        aria-label="Water Intake Calculator Card"
      >
        {/* Header */}
  <div className="text-center mb-6 sm:mb-8">
          <WaterDrop />
          <motion.h1 
            className="text-2xl xs:text-3xl sm:text-4xl font-bold mt-4 mb-2 bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent"
            initial={{ scale: 0.9 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            Hydration Calculator
          </motion.h1>
          <p className="text-gray-600 text-sm mb-2">
            Calculate your personalized daily water intake
          </p>
          <motion.p
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="text-cyan-600 text-xs italic font-medium"
            aria-live="polite"
          >
            {quote}
          </motion.p>
        </div>

  <div className="space-y-4 sm:space-y-6">
          <InputField
            id="weight"
            label="Body Weight"
            type="number"
            value={weight}
            onChange={(e) => setWeight(e.target.value)}
            placeholder="Enter your weight"
            unit="kg"
            min="1"
            max="500"
            step="0.1"
            tooltip="Your current body weight in kilograms"
            icon="⚖️"
            error={errors.weight}
          />

          <InputField
            id="exercise"
            label="Daily Exercise"
            type="number"
            value={exercise}
            onChange={(e) => setExercise(e.target.value)}
            placeholder="Hours of exercise"
            unit="hours"
            min="0"
            max="24"
            step="0.25"
            tooltip="Total hours of physical activity per day"
            icon="🏃‍♂️"
            error={errors.exercise}
          />

          <ClimateSelector
            value={climate}
            onChange={setClimate}
            error={errors.climate}
          />

          <div className="flex flex-col sm:flex-row gap-3 pt-2 sm:pt-4">
            <motion.button
              whileHover={{ scale: 1.04 }}
              whileTap={{ scale: 0.98 }}
              onClick={calculateWaterIntake}
              disabled={isCalculating}
              className="w-full sm:flex-1 flex justify-center items-center py-3 sm:py-4 px-4 sm:px-6 rounded-xl text-white text-base sm:text-lg font-semibold transition-all duration-300 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-4 focus:ring-cyan-300"
              aria-label="Calculate recommended water intake"
              tabIndex={0}
            >
              {isCalculating ? (
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="w-6 h-6 border-4 border-white border-t-transparent rounded-full"
                />
              ) : (
                <>
                  <WaterDrop size="sm" animate={false} />
                  <span className="ml-2">Calculate</span>
                </>
              )}
              {/* Ripple effect */}
              <span className="absolute inset-0 pointer-events-none" />
            </motion.button>
            {result && (
              <motion.button
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={reset}
                className="w-full sm:w-auto px-4 py-3 sm:py-4 rounded-xl border-2 border-gray-300 text-gray-600 hover:border-gray-400 hover:text-gray-700 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-300"
                aria-label="Reset calculator"
                tabIndex={0}
              >
                🔄
              </motion.button>
            )}
          </div>
        </div>

        <AnimatePresence>
          {result !== null && (
            <motion.div
              initial={{ opacity: 0, y: 20, scale: 0.9 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -20, scale: 0.9 }}
              className="mt-6 sm:mt-8 p-4 sm:p-6 bg-gradient-to-br from-blue-50 to-cyan-50 rounded-2xl border-2 border-blue-100 shadow-lg"
            >
              <div className="text-center mb-4 sm:mb-6">
                <WaterDrop size="sm" />
                <h3 className="text-lg sm:text-xl font-bold text-blue-800 mt-2 mb-1">
                  Your Daily Water Goal
                </h3>
                <div className="flex items-center justify-center space-x-2">
                  <motion.span 
                    className="text-3xl sm:text-4xl font-bold text-blue-600"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.2, type: "spring" }}
                  >
                    {result}
                  </motion.span>
                  <span className="text-base sm:text-xl text-blue-500 font-medium">Liters</span>
                </div>
                <p className="text-xs sm:text-sm text-blue-600 mt-2">
                  That's about {Math.round(result * 4)} cups of water! 🥤
                </p>
              </div>

              {showBreakdown && breakdown && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  transition={{ delay: 0.3 }}
                  className="space-y-2 sm:space-y-3 pt-3 sm:pt-4 border-t border-blue-200"
                >
                  <h4 className="font-semibold text-blue-800 text-xs sm:text-sm mb-2 sm:mb-3">Breakdown:</h4>
                  <ProgressBar current={breakdown.base} total={breakdown.total} label="Base needs" />
                  {breakdown.exercise > 0 && (
                    <ProgressBar current={breakdown.exercise} total={breakdown.total} label="Exercise boost" />
                  )}
                  {breakdown.climate > 0 && (
                    <ProgressBar current={breakdown.climate} total={breakdown.total} label="Climate adjustment" />
                  )}
                </motion.div>
              )}

              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5 }}
                className="mt-4 sm:mt-6 p-3 sm:p-4 bg-white/60 rounded-xl"
              >
                <h4 className="font-semibold text-gray-700 text-xs sm:text-sm mb-1 sm:mb-2">💡 Hydration Tips:</h4>
                <ul className="text-xs text-gray-600 space-y-1">
                  <li>• Start your day with a glass of water</li>
                  <li>• Drink before, during, and after exercise</li>
                  <li>• Keep a water bottle with you</li>
                  <li>• Listen to your body's thirst signals</li>
                </ul>
              </motion.div>
              <div className="mt-6 flex justify-center">
                <Ad slot="1234567890" />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </div>
  );
}