"use client";

import { motion } from 'framer-motion';

interface SkeletonLoaderProps {
  variant?: 'text' | 'circle' | 'rectangle' | 'card';
  width?: string | number;
  height?: string | number;
  className?: string;
  animate?: boolean;
}

const SkeletonLoader = ({ 
  variant = 'rectangle', 
  width = '100%', 
  height = '20px', 
  className = '',
  animate = true 
}: SkeletonLoaderProps) => {
  const baseClasses = "bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700";
  
  const variantClasses = {
    text: "rounded-md",
    circle: "rounded-full",
    rectangle: "rounded-lg",
    card: "rounded-xl"
  };

  const shimmerAnimation = {
    backgroundPosition: ['200% 0', '-200% 0'],
  };

  return (
    <motion.div
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
      style={{ 
        width, 
        height,
        backgroundSize: '200% 100%'
      }}
      animate={animate ? shimmerAnimation : {}}
      transition={animate ? {
        duration: 2,
        repeat: Infinity,
        ease: "linear"
      } : {}}
    />
  );
};

// Skeleton components for specific use cases
export const SkeletonText = ({ lines = 1, className = '' }: { lines?: number; className?: string }) => (
  <div className={`space-y-2 ${className}`}>
    {Array.from({ length: lines }).map((_, i) => (
      <SkeletonLoader
        key={i}
        variant="text"
        height="16px"
        width={i === lines - 1 ? '75%' : '100%'}
      />
    ))}
  </div>
);

export const SkeletonCard = ({ className = '' }: { className?: string }) => (
  <div className={`p-6 space-y-4 ${className}`}>
    <div className="flex items-center space-x-4">
      <SkeletonLoader variant="circle" width="48px" height="48px" />
      <div className="flex-1 space-y-2">
        <SkeletonLoader variant="text" height="20px" width="60%" />
        <SkeletonLoader variant="text" height="16px" width="40%" />
      </div>
    </div>
    <SkeletonText lines={3} />
  </div>
);

export const SkeletonButton = ({ className = '' }: { className?: string }) => (
  <SkeletonLoader
    variant="rectangle"
    width="120px"
    height="44px"
    className={className}
  />
);

export const SkeletonInput = ({ className = '' }: { className?: string }) => (
  <div className={`space-y-2 ${className}`}>
    <SkeletonLoader variant="text" width="80px" height="16px" />
    <SkeletonLoader variant="rectangle" width="100%" height="48px" />
  </div>
);

// Loading state for the calculator
export const CalculatorSkeleton = () => (
  <div className="space-y-6">
    {/* Header skeleton */}
    <div className="text-center space-y-4">
      <SkeletonLoader variant="circle" width="48px" height="48px" className="mx-auto" />
      <SkeletonLoader variant="text" width="250px" height="32px" className="mx-auto" />
      <SkeletonLoader variant="text" width="300px" height="16px" className="mx-auto" />
    </div>
    
    {/* Form skeleton */}
    <div className="space-y-6">
      <SkeletonInput />
      <SkeletonInput />
      <div className="grid grid-cols-2 gap-3">
        <SkeletonLoader variant="card" height="80px" />
        <SkeletonLoader variant="card" height="80px" />
      </div>
      <SkeletonButton className="w-full h-12" />
    </div>
  </div>
);

export default SkeletonLoader;
