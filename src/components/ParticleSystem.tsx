"use client";

import { motion } from 'framer-motion';
import { useMemo } from 'react';

interface Particle {
  id: number;
  x: number;
  y: number;
  size: number;
  duration: number;
  delay: number;
  opacity: number;
  color: string;
}

interface ParticleSystemProps {
  count?: number;
  theme?: 'light' | 'dark';
}

const ParticleSystem = ({ count = 20, theme = 'light' }: ParticleSystemProps) => {
  const particles = useMemo(() => {
    const particleArray: Particle[] = [];
    
    for (let i = 0; i < count; i++) {
      particleArray.push({
        id: i,
        x: Math.random() * 100,
        y: Math.random() * 100,
        size: Math.random() * 4 + 1,
        duration: Math.random() * 20 + 10,
        delay: Math.random() * 5,
        opacity: Math.random() * 0.6 + 0.1,
        color: theme === 'dark' 
          ? `hsl(${200 + Math.random() * 60}, 70%, ${60 + Math.random() * 20}%)`
          : `hsl(${200 + Math.random() * 60}, 60%, ${40 + Math.random() * 30}%)`
      });
    }
    
    return particleArray;
  }, [count, theme]);

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          className="absolute rounded-full"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            backgroundColor: particle.color,
            opacity: particle.opacity,
          }}
          animate={{
            y: [0, -30, 0],
            x: [0, Math.sin(particle.id) * 20, 0],
            scale: [1, 1.2, 1],
            opacity: [particle.opacity, particle.opacity * 0.3, particle.opacity],
          }}
          transition={{
            duration: particle.duration,
            repeat: Infinity,
            delay: particle.delay,
            ease: "easeInOut",
          }}
        />
      ))}
      
      {/* Floating water droplets */}
      {[...Array(6)].map((_, i) => (
        <motion.div
          key={`droplet-${i}`}
          className="absolute"
          style={{
            left: `${20 + i * 15}%`,
            top: `${30 + (i % 3) * 25}%`,
          }}
          animate={{
            y: [0, -40, 0],
            rotate: [0, 10, -10, 0],
            scale: [1, 1.1, 1],
          }}
          transition={{
            duration: 8 + i * 2,
            repeat: Infinity,
            delay: i * 1.5,
            ease: "easeInOut",
          }}
        >
          <div className={`w-3 h-3 rounded-full ${
            theme === 'dark' 
              ? 'bg-gradient-to-br from-blue-400/30 to-cyan-400/30' 
              : 'bg-gradient-to-br from-blue-300/40 to-cyan-300/40'
          } backdrop-blur-sm`} />
        </motion.div>
      ))}
    </div>
  );
};

export default ParticleSystem;
