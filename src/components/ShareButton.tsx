"use client";

import { motion } from 'framer-motion';
import { useState } from 'react';
import EnhancedButton from './EnhancedButton';

interface ShareButtonProps {
  result: number;
  weight: string;
  exercise: string;
  climate: string;
}

const ShareButton = ({ result, weight, exercise, climate }: ShareButtonProps) => {
  const [showShareMenu, setShowShareMenu] = useState(false);
  const [copied, setCopied] = useState(false);

  const shareText = `💧 My daily hydration goal: ${result}L (${Math.round(result * 4)} cups)
📊 Based on: ${weight}kg body weight, ${exercise}h exercise, ${climate.toLowerCase()} climate
🌟 Calculate yours at: ${window.location.origin}`;

  const handleShare = async (method: 'copy' | 'twitter' | 'facebook' | 'whatsapp') => {
    switch (method) {
      case 'copy':
        try {
          await navigator.clipboard.writeText(shareText);
          setCopied(true);
          setTimeout(() => setCopied(false), 2000);
        } catch (err) {
          console.error('Failed to copy:', err);
        }
        break;
      
      case 'twitter':
        const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}`;
        window.open(twitterUrl, '_blank');
        break;
      
      case 'facebook':
        const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.origin)}&quote=${encodeURIComponent(shareText)}`;
        window.open(facebookUrl, '_blank');
        break;
      
      case 'whatsapp':
        const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(shareText)}`;
        window.open(whatsappUrl, '_blank');
        break;
    }
    setShowShareMenu(false);
  };

  const shareOptions = [
    { id: 'copy', label: copied ? 'Copied!' : 'Copy Link', icon: copied ? '✅' : '📋', color: 'bg-gray-500' },
    { id: 'twitter', label: 'Twitter', icon: '🐦', color: 'bg-blue-500' },
    { id: 'facebook', label: 'Facebook', icon: '📘', color: 'bg-blue-600' },
    { id: 'whatsapp', label: 'WhatsApp', icon: '💬', color: 'bg-green-500' },
  ];

  return (
    <div className="relative">
      <EnhancedButton
        onClick={() => setShowShareMenu(!showShareMenu)}
        variant="ghost"
        size="sm"
        icon="🔗"
        className="text-primary-600 dark:text-primary-400"
      >
        Share Result
      </EnhancedButton>

      {showShareMenu && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-40"
            onClick={() => setShowShareMenu(false)}
          />
          
          {/* Share menu */}
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            className="absolute top-full mt-2 right-0 z-50 bg-white/95 dark:bg-gray-800/95 backdrop-blur-xl rounded-xl border border-gray-200 dark:border-gray-700 shadow-2xl overflow-hidden min-w-[200px]"
          >
            {shareOptions.map((option) => (
              <motion.button
                key={option.id}
                onClick={() => handleShare(option.id as any)}
                className="w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                whileHover={{ x: 4 }}
                whileTap={{ scale: 0.98 }}
              >
                <span className="text-lg">{option.icon}</span>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-200">
                  {option.label}
                </span>
              </motion.button>
            ))}
          </motion.div>
        </>
      )}
    </div>
  );
};

export default ShareButton;
