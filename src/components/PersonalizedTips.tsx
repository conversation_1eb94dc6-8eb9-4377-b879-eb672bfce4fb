"use client";

import { motion } from 'framer-motion';
import { useMemo } from 'react';

interface PersonalizedTipsProps {
  result: number;
  weight: number;
  exercise: number;
  climate: string;
}

const PersonalizedTips = ({ result, weight, exercise, climate }: PersonalizedTipsProps) => {
  const tips = useMemo(() => {
    const personalizedTips = [];
    
    // Weight-based tips
    if (weight < 60) {
      personalizedTips.push({
        icon: "⚖️",
        title: "Lighter Body Weight",
        tip: "Your smaller body size means you need less water, but consistency is key. Drink small amounts frequently.",
        category: "weight"
      });
    } else if (weight > 80) {
      personalizedTips.push({
        icon: "💪",
        title: "Higher Body Weight",
        tip: "Your larger body mass requires more hydration. Consider drinking a glass of water every hour.",
        category: "weight"
      });
    }

    // Exercise-based tips
    if (exercise === 0) {
      personalizedTips.push({
        icon: "🪑",
        title: "Sedentary Lifestyle",
        tip: "Even without exercise, stay hydrated! Set hourly reminders to drink water throughout the day.",
        category: "exercise"
      });
    } else if (exercise > 0 && exercise <= 1) {
      personalizedTips.push({
        icon: "🚶‍♂️",
        title: "Light Activity",
        tip: "Great job staying active! Drink water before and after your workouts to maintain hydration.",
        category: "exercise"
      });
    } else if (exercise > 1) {
      personalizedTips.push({
        icon: "🏃‍♂️",
        title: "Active Lifestyle",
        tip: "Your high activity level is impressive! Drink 500ml extra for each hour of intense exercise.",
        category: "exercise"
      });
    }

    // Climate-based tips
    if (climate === "Hot") {
      personalizedTips.push({
        icon: "🌡️",
        title: "Hot Climate",
        tip: "Hot weather increases water loss through sweat. Drink cool water and avoid peak sun hours.",
        category: "climate"
      });
    } else {
      personalizedTips.push({
        icon: "🌤️",
        title: "Moderate Climate",
        tip: "Perfect weather for staying hydrated! Room temperature water is ideal for absorption.",
        category: "climate"
      });
    }

    // Result-based tips
    if (result < 2) {
      personalizedTips.push({
        icon: "💧",
        title: "Moderate Hydration",
        tip: "Your hydration needs are moderate. Spread your intake evenly throughout the day.",
        category: "result"
      });
    } else if (result >= 2 && result < 3) {
      personalizedTips.push({
        icon: "🌊",
        title: "Good Hydration",
        tip: "You need a good amount of water daily. Consider carrying a 1L bottle and refilling it.",
        category: "result"
      });
    } else {
      personalizedTips.push({
        icon: "🏊‍♂️",
        title: "High Hydration",
        tip: "You need substantial hydration! Consider electrolyte supplements for better absorption.",
        category: "result"
      });
    }

    // General tips
    personalizedTips.push({
      icon: "⏰",
      title: "Timing Matters",
      tip: "Start your day with 2 glasses of water and drink consistently rather than large amounts at once.",
      category: "general"
    });

    personalizedTips.push({
      icon: "🍎",
      title: "Food Sources",
      tip: "20% of your hydration comes from food. Include water-rich fruits and vegetables in your diet.",
      category: "general"
    });

    return personalizedTips;
  }, [result, weight, exercise, climate]);

  const categoryColors = {
    weight: "from-purple-500/20 to-pink-500/20",
    exercise: "from-green-500/20 to-emerald-500/20",
    climate: "from-orange-500/20 to-red-500/20",
    result: "from-blue-500/20 to-cyan-500/20",
    general: "from-gray-500/20 to-slate-500/20"
  };

  return (
    <div className="space-y-4">
      <motion.h4 
        className="font-bold text-primary-700 dark:text-primary-300 text-base flex items-center gap-2"
        initial={{ opacity: 0, x: -10 }}
        animate={{ opacity: 1, x: 0 }}
      >
        <span className="text-lg">🎯</span>
        Personalized for You
      </motion.h4>
      
      <div className="grid gap-3">
        {tips.map((tip, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className={`p-4 rounded-xl bg-gradient-to-r ${categoryColors[tip.category]} border border-white/20 dark:border-gray-600/20 backdrop-blur-sm hover:scale-[1.02] transition-transform duration-200`}
          >
            <div className="flex items-start gap-3">
              <motion.span 
                className="text-xl flex-shrink-0 mt-0.5"
                whileHover={{ scale: 1.2, rotate: 5 }}
                transition={{ duration: 0.2 }}
              >
                {tip.icon}
              </motion.span>
              <div className="flex-1">
                <h5 className="font-semibold text-gray-800 dark:text-gray-200 text-sm mb-1">
                  {tip.title}
                </h5>
                <p className="text-xs text-gray-600 dark:text-gray-300 leading-relaxed">
                  {tip.tip}
                </p>
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default PersonalizedTips;
