# 💧 Hydration Calculator

A modern, responsive water intake calculator built with Next.js, React, TypeScript, and Tailwind CSS. Calculate your personalized daily water intake based on your body weight, exercise level, and climate conditions.

## ✨ Features

### 🎨 Modern UI/UX
- **Responsive Design**: Mobile-first approach with seamless experience across all devices
- **Dark/Light Mode**: Full theme support with system preference detection
- **Smooth Animations**: Powered by Framer Motion for delightful micro-interactions
- **Glass Morphism**: Modern backdrop blur effects and translucent elements
- **Gradient Backgrounds**: Dynamic animated gradients that respond to user interactions

### ♿ Accessibility
- **Screen Reader Support**: Comprehensive ARIA labels and live regions
- **Keyboard Navigation**: Full keyboard accessibility with focus management
- **Skip Links**: Quick navigation for assistive technologies
- **High Contrast**: Optimized color schemes for better visibility
- **Semantic HTML**: Proper heading hierarchy and landmark elements

### 🔧 Technical Features
- **TypeScript**: Full type safety and better developer experience
- **Theme Persistence**: Remembers user's theme preference across sessions
- **No FOUC**: Prevents flash of unstyled content with theme pre-loading
- **Performance Optimized**: Lazy loading and efficient re-renders
- **SEO Friendly**: Proper meta tags and structured data

### 📊 Calculator Features
- **Personalized Results**: Based on weight, exercise, and climate
- **Visual Breakdown**: Progress bars showing calculation components
- **Multiple Units**: Results in both liters and cups
- **Hydration Tips**: Contextual advice for better hydration habits
- **Input Validation**: Real-time error checking and user feedback

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd water-intake-calculator
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🛠️ Tech Stack

- **Framework**: Next.js 15.5.3
- **Language**: TypeScript
- **Styling**: Tailwind CSS 4.1.13
- **Animations**: Framer Motion 12.23.12
- **Fonts**: Geist Sans & Geist Mono
- **Icons**: Unicode Emojis for universal compatibility

## 📱 Responsive Breakpoints

- **xs**: 475px (Extra small phones)
- **sm**: 640px (Small phones)
- **md**: 768px (Tablets)
- **lg**: 1024px (Small laptops)
- **xl**: 1280px (Desktops)
- **2xl**: 1536px (Large screens)

## 🎨 Theme System

The application supports three theme modes:
- **Light**: Clean, bright interface
- **Dark**: Easy on the eyes for low-light environments
- **System**: Automatically matches your device's preference

Theme preferences are persisted in localStorage and applied before page load to prevent flickering.

## 🧮 Calculation Formula

The water intake calculation uses the following formula:

```
Base Intake = Body Weight (kg) × 35ml
Exercise Adjustment = Exercise Hours × 500ml
Climate Adjustment = 700ml (if hot climate)
Total Daily Intake = Base + Exercise + Climate
```

## 🔧 Customization

### Colors
The app uses a custom color palette defined in `tailwind.config.ts`:
- **Primary**: Blue tones for main actions and highlights
- **Secondary**: Teal/cyan tones for accents and secondary elements

### Animations
Custom animations are defined in the Tailwind config:
- `float`: Gentle floating motion for decorative elements
- `pulse-slow`: Slower pulse animation for subtle emphasis
- `fade-in`, `slide-up`, `scale-in`: Entrance animations

## 📦 Project Structure

```
src/
├── app/
│   ├── globals.css          # Global styles and CSS variables
│   ├── layout.tsx           # Root layout with theme provider
│   └── page.tsx             # Main calculator component
├── components/
│   ├── Ad.tsx               # Advertisement component
│   └── ThemeToggle.tsx      # Theme switcher component
└── contexts/
    └── ThemeContext.tsx     # Theme management context
```

## 🚀 Deployment

The app is optimized for deployment on Vercel:

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy with zero configuration

For other platforms, build the app:
```bash
npm run build
npm start
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Design inspiration from modern health and fitness apps
- Accessibility guidelines from WCAG 2.1
- Animation patterns from Material Design and Apple HIG
